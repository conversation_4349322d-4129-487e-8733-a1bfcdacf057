// تكوين الفيديوهات التعليمية (Reels) للتكامل مع الباك إند

class ReelConfig {
  // إعدادات الفيديو
  static const int maxVideoDuration = 300; // 5 دقائق بالثواني
  static const int minVideoDuration = 15; // 15 ثانية
  static const int maxVideoSize = 100 * 1024 * 1024; // 100 MB
  static const int maxThumbnailSize = 5 * 1024 * 1024; // 5 MB
  
  // جودات الفيديو المدعومة
  static const List<String> supportedQualities = ['360p', '480p', '720p', '1080p'];
  static const String defaultQuality = '720p';
  
  // أنواع الملفات المدعومة
  static const List<String> supportedVideoTypes = [
    'video/mp4',
    'video/mov',
    'video/avi',
    'video/mkv',
    'video/webm'
  ];
  
  static const List<String> supportedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/webp'
  ];
  
  // فئات المحتوى التعليمي
  static const Map<String, String> categories = {
    'programming': 'البرمجة',
    'mathematics': 'الرياضيات',
    'science': 'العلوم',
    'languages': 'اللغات',
    'history': 'التاريخ',
    'geography': 'الجغرافيا',
    'arts': 'الفنون',
    'music': 'الموسيقى',
    'sports': 'الرياضة',
    'cooking': 'الطبخ',
    'technology': 'التكنولوجيا',
    'business': 'الأعمال',
    'health': 'الصحة',
    'psychology': 'علم النفس',
    'philosophy': 'الفلسفة',
    'religion': 'الدين',
    'general': 'عام',
  };
  
  // مستويات الصعوبة
  static const Map<String, String> difficultyLevels = {
    'beginner': 'مبتدئ',
    'intermediate': 'متوسط',
    'advanced': 'متقدم',
    'expert': 'خبير',
  };
  
  // أنواع الترتيب
  static const Map<String, String> sortOptions = {
    'latest': 'الأحدث',
    'popular': 'الأكثر شعبية',
    'most_liked': 'الأكثر إعجاباً',
    'most_viewed': 'الأكثر مشاهدة',
    'highest_rated': 'الأعلى تقييماً',
    'shortest': 'الأقصر',
    'longest': 'الأطول',
  };
  
  // إعدادات الكويز
  static const int maxQuizOptions = 6;
  static const int minQuizOptions = 2;
  static const int maxQuestionLength = 200;
  static const int maxOptionLength = 100;
  static const int maxExplanationLength = 500;
  static const int defaultQuizPoints = 10;
  
  // إعدادات التعليقات
  static const int maxCommentLength = 500;
  static const int maxReplyDepth = 3;
  static const int commentsPerPage = 20;
  
  // إعدادات التفاعل
  static const List<String> reactionTypes = ['like', 'dislike', 'love', 'wow', 'haha', 'sad', 'angry'];
  static const Duration minWatchTimeForView = Duration(seconds: 3);
  static const Duration autoPlayDelay = Duration(milliseconds: 500);
  
  // إعدادات الواجهة
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const double videoAspectRatio = 9 / 16; // عمودي
  static const int maxVisibleReels = 3; // عدد الفيديوهات المحملة مسبقاً
  
  // إعدادات التخزين المؤقت
  static const Duration cacheExpiry = Duration(hours: 1);
  static const int maxCachedReels = 50;
  static const int maxCachedComments = 200;
  
  // إعدادات الشبكة
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration uploadTimeout = Duration(minutes: 10);
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // رسائل الأخطاء
  static const Map<String, String> errorMessages = {
    'video_too_large': 'حجم الفيديو كبير جداً',
    'video_too_short': 'الفيديو قصير جداً',
    'video_too_long': 'الفيديو طويل جداً',
    'unsupported_format': 'تنسيق الفيديو غير مدعوم',
    'upload_failed': 'فشل في رفع الفيديو',
    'network_error': 'خطأ في الشبكة',
    'server_error': 'خطأ في الخادم',
    'auth_error': 'خطأ في المصادقة',
    'permission_denied': 'ليس لديك صلاحية',
    'video_not_found': 'الفيديو غير موجود',
    'comment_too_long': 'التعليق طويل جداً',
    'invalid_quiz': 'بيانات الكويز غير صحيحة',
    'quota_exceeded': 'تم تجاوز الحد المسموح',
  };
  
  // إعدادات الإشعارات
  static const Map<String, bool> defaultNotificationSettings = {
    'new_follower': true,
    'video_liked': true,
    'video_commented': true,
    'quiz_completed': true,
    'milestone_reached': true,
    'trending_video': false,
    'weekly_summary': true,
  };
  
  // معايير الجودة
  static const Map<String, Map<String, int>> qualitySettings = {
    '360p': {'width': 640, 'height': 360, 'bitrate': 800},
    '480p': {'width': 854, 'height': 480, 'bitrate': 1200},
    '720p': {'width': 1280, 'height': 720, 'bitrate': 2500},
    '1080p': {'width': 1920, 'height': 1080, 'bitrate': 5000},
  };
  
  // دوال التحقق من صحة البيانات
  static bool isValidVideoSize(int fileSize) {
    return fileSize <= maxVideoSize;
  }
  
  static bool isValidVideoDuration(int durationSeconds) {
    return durationSeconds >= minVideoDuration && durationSeconds <= maxVideoDuration;
  }
  
  static bool isValidVideoType(String mimeType) {
    return supportedVideoTypes.contains(mimeType.toLowerCase());
  }
  
  static bool isValidImageType(String mimeType) {
    return supportedImageTypes.contains(mimeType.toLowerCase());
  }
  
  static bool isValidCategory(String category) {
    return categories.containsKey(category);
  }
  
  static bool isValidDifficulty(String difficulty) {
    return difficultyLevels.containsKey(difficulty);
  }
  
  static bool isValidCommentLength(String comment) {
    return comment.trim().isNotEmpty && comment.length <= maxCommentLength;
  }
  
  static bool isValidQuizQuestion(String question) {
    return question.trim().isNotEmpty && question.length <= maxQuestionLength;
  }
  
  static bool isValidQuizOptions(List<String> options) {
    return options.length >= minQuizOptions && 
           options.length <= maxQuizOptions &&
           options.every((option) => option.trim().isNotEmpty && option.length <= maxOptionLength);
  }
  
  // دوال التنسيق
  static String formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
  
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  static String formatViewCount(int views) {
    if (views < 1000) return views.toString();
    if (views < 1000000) return '${(views / 1000).toStringAsFixed(1)}K';
    return '${(views / 1000000).toStringAsFixed(1)}M';
  }
  
  static String formatLikeCount(int likes) {
    if (likes < 1000) return likes.toString();
    if (likes < 1000000) return '${(likes / 1000).toStringAsFixed(1)}K';
    return '${(likes / 1000000).toStringAsFixed(1)}M';
  }
  
  static String getCategoryName(String categoryKey) {
    return categories[categoryKey] ?? 'غير محدد';
  }
  
  static String getDifficultyName(String difficultyKey) {
    return difficultyLevels[difficultyKey] ?? 'غير محدد';
  }
  
  static String getSortOptionName(String sortKey) {
    return sortOptions[sortKey] ?? 'غير محدد';
  }
  
  // دوال التحليل
  static String getErrorMessage(String errorType, [String? customMessage]) {
    return customMessage ?? errorMessages[errorType] ?? 'خطأ غير معروف';
  }
  
  static bool isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
           errorString.contains('connection') ||
           errorString.contains('timeout') ||
           errorString.contains('unreachable');
  }
  
  static bool isServerError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('500') ||
           errorString.contains('502') ||
           errorString.contains('503') ||
           errorString.contains('504') ||
           errorString.contains('server error');
  }
  
  static bool isAuthError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('401') ||
           errorString.contains('unauthorized') ||
           errorString.contains('authentication') ||
           errorString.contains('token');
  }
  
  // حساب نقاط الكويز حسب الصعوبة
  static int calculateQuizPoints(String difficulty, bool isCorrect) {
    if (!isCorrect) return 0;
    
    switch (difficulty) {
      case 'beginner':
        return 5;
      case 'intermediate':
        return 10;
      case 'advanced':
        return 15;
      case 'expert':
        return 20;
      default:
        return defaultQuizPoints;
    }
  }
  
  // تحديد جودة الفيديو المناسبة حسب سرعة الإنترنت
  static String getOptimalQuality(double connectionSpeed) {
    // connectionSpeed in Mbps
    if (connectionSpeed >= 5.0) return '1080p';
    if (connectionSpeed >= 2.5) return '720p';
    if (connectionSpeed >= 1.0) return '480p';
    return '360p';
  }
  
  // تحديد ما إذا كان يجب تشغيل الفيديو تلقائياً
  static bool shouldAutoPlay(bool isWifi, bool dataSaverMode) {
    return isWifi && !dataSaverMode;
  }
}
