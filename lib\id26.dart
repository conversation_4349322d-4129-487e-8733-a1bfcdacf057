import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:confetti/confetti.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';

class ReelsScreen extends StatefulWidget {
  final VoidCallback onToggleTheme;
  const ReelsScreen({super.key, required this.onToggleTheme});

  @override
  ReelsScreenState createState() => ReelsScreenState();
}

class ReelsScreenState extends State<ReelsScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentReel = 0;
  int _correctAnswers = 0;
  String _preferredQuality = '720p';
  final List<Reel> _reels = [
    Reel(
      id: 1,
      videoUrls: {
        '360p': 'https://www.w3schools.com/html/mov_bbb.mp4',
        '720p': 'https://www.w3schools.com/html/mov_bbb.mp4',
      },
      fallbackAsset: 'assets/fallback_video.mp4',
      username: 'User1',
      description: 'Learn Python programming basics',
      educationalContent:
      'Python is a versatile and easy-to-learn programming language...',
      likes: 120,
      comments: 45,
      shares: 10,
      views: 500,
      quizQuestion: 'What language was discussed?',
      quizOptions: ['Python', 'Java', 'C++', 'Ruby'],
      correctAnswer: 'Python',
      isFollowing: false,
    ),
    Reel(
      id: 2,
      videoUrls: {
        '360p': 'https://www.w3schools.com/html/mov_bbb.mp4',
        '720p': 'https://www.w3schools.com/html/mov_bbb.mp4',
      },
      fallbackAsset: 'assets/fallback_video.mp4',
      username: 'User2',
      description: 'Discover applied mathematics secrets',
      educationalContent: 'Applied mathematics is used in many fields...',
      likes: 200,
      comments: 30,
      shares: 15,
      views: 750,
      quizQuestion: 'What field uses applied mathematics?',
      quizOptions: ['Engineering', 'Cooking', 'Painting', 'Writing'],
      correctAnswer: 'Engineering',
      isFollowing: false,
    ),
  ];
  final Map<int, VideoPlayerController> _controllers = {};
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  final Map<int, int> _retryCounts = {};

  @override
  void initState() {
    super.initState();
    _loadPreferredQuality();
    _preloadVideos();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation =
        CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut);
    _fadeController.forward();
  }

  void _loadPreferredQuality() async {
    final prefs = await SharedPreferences.getInstance();
    _preferredQuality = prefs.getString('preferredQuality') ?? '720p';
  }

  void _preloadVideos() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) return;
    if (connectivityResult != ConnectivityResult.wifi) {
      final reel = _reels[_currentReel];
      if (!_controllers.containsKey(reel.id)) {
        _initializeVideoController(reel, _currentReel);
      }
      return;
    }

    final preloadIndices = [
      _currentReel,
      if (_currentReel + 1 < _reels.length) _currentReel + 1,
      if (_currentReel - 1 >= 0) _currentReel - 1,
    ];

    for (var index in preloadIndices) {
      final reel = _reels[index];
      if (!_controllers.containsKey(reel.id)) {
        _initializeVideoController(reel, index);
      }
    }

    _controllers.removeWhere((id, controller) {
      if (!preloadIndices
          .contains(_reels.indexWhere((reel) => reel.id == id))) {
        controller.pause();
        controller.dispose();
        return true;
      }
      return false;
    });
  }

  void _initializeVideoController(Reel reel, int index, {int retryCount = 0}) {
    final controller = retryCount >= 3 && reel.fallbackAsset != null
        ? VideoPlayerController.asset(reel.fallbackAsset!)
        : VideoPlayerController.network(
        reel.videoUrls[reel.videoUrls.containsKey(_preferredQuality)
            ? _preferredQuality
            : reel.videoUrls.keys.first]!);
    _controllers[reel.id] = controller;

    controller.initialize().then((_) {
      if (!mounted) return;
      setState(() {
        _retryCounts.remove(reel.id);
        if (index == _currentReel) {
          controller.play();
        }
      });
    }).catchError((error) {
      if (retryCount < 3) {
        _retryCounts[reel.id] = (retryCount + 1);
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            _initializeVideoController(reel, index, retryCount: retryCount + 1);
          }
        });
      } else if (mounted) {
        // Ensure context is accessed only when mounted
        ScaffoldMessenger.of(this.context).showSnackBar(
          SnackBar(
              content: Text('Failed to load video ${reel.id}, using fallback')),
        );
      }
    });
  }

  void _handleReelQualityChange(Reel reel, String newQuality) {
    if (!mounted) return;

    final oldController = _controllers[reel.id];
    oldController?.pause();
    // Important: Dispose the old controller *after* the new one is ready or if creation fails.
    // For now, we'll dispose it before creating the new one, assuming creation is quick.
    // A more robust solution might involve holding onto the old one until the new one is initialized.
    oldController?.dispose();

    final String? videoUrl = reel.videoUrls[newQuality];
    if (videoUrl == null) {
      print(
          "Error: Video URL for quality '$newQuality' not found for reel ${reel.id}");
      // Optionally, re-initialize with a default or fallback if the quality URL is missing
      // _initializeVideoController(reel, _reels.indexWhere((r) => r.id == reel.id));
      return;
    }

    _preferredQuality = newQuality;
    _savePreferredQuality(newQuality);
    final newController = VideoPlayerController.network(videoUrl);
    _controllers[reel.id] = newController;

    // We need to call setState to ensure the ReelWidget gets the new controller instance.
    setState(() {});

    newController.initialize().then((_) {
      if (!mounted) {
        newController
            .dispose(); // Dispose if not mounted by the time init completes
        return;
      }
      // Check if this reel is still the current one before playing
      final currentReelIndex = _reels.indexWhere((r) => r.id == reel.id);
      if (currentReelIndex == _currentReel) {
        newController.play();
      }
      // Update state again to reflect that the controller is initialized (e.g., hide loading indicator in ReelWidget)
      // This might require ReelWidget to expose a way to signal its loading state or for ReelsScreenState
      // to manage loading states per reel if _isLoading in ReelWidget is to be driven by this.
      // For now, ReelWidget's own initState handles its _isLoading based on controller.initialize().
      if (mounted) setState(() {});
    }).catchError((error) {
      print(
          "Error initializing new controller for reel ${reel.id} quality $newQuality: $error");
      // Handle error: maybe try fallback or show error message
      // Potentially remove the failed controller or re-initialize with fallback
      if (mounted) {
        _controllers.remove(reel.id); // Remove the failed controller
        // Attempt to re-initialize with fallback or default
        _initializeVideoController(
            reel, _reels.indexWhere((r) => r.id == reel.id),
            retryCount: 3); // Force fallback if possible
        setState(() {});
      }
    });
  }

  void _savePreferredQuality(String quality) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('preferredQuality', quality);
  }

  void _onQuizAnswered(bool isCorrect) {
    if (isCorrect) {
      setState(() {
        _correctAnswers++;
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          await Future.delayed(const Duration(seconds: 1));
          setState(() {
            _reels.add(Reel(
              id: _reels.length + 1,
              videoUrls: {
                '360p': 'https://www.w3schools.com/html/mov_bbb.mp4',
                '720p': 'https://www.w3schools.com/html/mov_bbb.mp4',
              },
              fallbackAsset: 'assets/fallback_video.mp4',
              username: 'User${_reels.length + 1}',
              description: 'New educational content',
              educationalContent: 'Learn something new...',
              likes: 0,
              comments: 0,
              shares: 0,
              views: 0,
              quizQuestion: 'What is new?',
              quizOptions: ['A', 'B', 'C', 'D'],
              correctAnswer: 'A',
              isFollowing: false,
            ));
          });
        },
        child: Stack(
          children: [
            PageView.builder(
              scrollDirection: Axis.vertical,
              controller: _pageController,
              itemCount: _reels.length,
              onPageChanged: (index) {
                setState(() {
                  _currentReel = index;
                  _fadeController.reset();
                  _fadeController.forward();
                  for (var controller in _controllers.values) {
                    controller.pause();
                  }
                  _controllers[_reels[index].id]?.play();
                  _preloadVideos();
                  _reels[index].views++;
                });
              },
              itemBuilder: (context, index) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: ReelWidget(
                    reel: _reels[index],
                    controller: _controllers[_reels[index].id],
                    onFollow: () {
                      setState(() {
                        _reels[index].isFollowing = !_reels[index].isFollowing;
                      });
                    },
                    onShare: () {
                      Share.share(
                        'Check out this educational video: ${_reels[index].description} - ${_reels[index].videoUrls['720p']}',
                      );
                    },
                    onComment: () => _showComments(context, _reels[index]),
                    onQuizAnswered: _onQuizAnswered,
                    onQualityChanged: (newQuality) {
                      _handleReelQualityChange(_reels[index], newQuality);
                    },
                  ),
                );
              },
            ),
            Positioned(
              top: 10,
              left: 10,
              child: IconButton(
                icon: Icon(
                  Theme.of(context).brightness == Brightness.dark
                      ? Icons.light_mode
                      : Icons.dark_mode,
                  color: Colors.white,
                ),
                onPressed: widget.onToggleTheme,
                tooltip: 'Toggle theme',
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showComments(BuildContext context, Reel reel) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => CommentSection(reel: reel),
    );
  }
}

class Reel {
  final int id;
  final Map<String, String> videoUrls;
  final String? fallbackAsset;
  final String username;
  final String description;
  final String educationalContent;
  int likes;
  int comments;
  int shares;
  int views;
  final String quizQuestion;
  final List<String> quizOptions;
  final String correctAnswer;
  bool isFollowing;

  Reel({
    required this.id,
    required this.videoUrls,
    this.fallbackAsset,
    required this.username,
    required this.description,
    required this.educationalContent,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.views,
    required this.quizQuestion,
    required this.quizOptions,
    required this.correctAnswer,
    required this.isFollowing,
  });
}

class ReelWidget extends StatefulWidget {
  final Reel reel;
  final VideoPlayerController? controller;
  final VoidCallback onFollow;
  final VoidCallback onShare;
  final VoidCallback onComment;
  final Function(bool) onQuizAnswered;
  final void Function(String newQuality)? onQualityChanged;

  const ReelWidget({
    super.key,
    required this.reel,
    this.controller,
    required this.onFollow,
    required this.onShare,
    required this.onComment,
    required this.onQuizAnswered,
    this.onQualityChanged,
  });

  @override
  ReelWidgetState createState() => ReelWidgetState();
}

class ReelWidgetState extends State<ReelWidget>
    with TickerProviderStateMixin {
  late final AnimationController _skeletonController;
  bool _isLoading = true;
  // ignore: unused_field
  double _progress = 0.0;
  bool _showQuiz = false;
  String? _selectedAnswer;
  bool? _isCorrect;
  late AnimationController _animationController;
  late Animation<double> _buttonAnimation;
  final List<Map<String, dynamic>> _reactions = [];
  int _likes;
  bool _isFullScreen = false;
  bool _isPlaying = true;
  late ConfettiController _confettiController;
  // --- New UX state ---
  bool _isMuted = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  ReelWidgetState() : _likes = 0;

  @override
  void initState() {
    super.initState();
    _likes = widget.reel.likes;
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 2));
    if (widget.controller != null) {
      widget.controller!.initialize().then((_) {
        _totalDuration = widget.controller!.value.duration;
        setState(() {
          _isLoading = false;
          widget.controller!.play();
          widget.controller!.setLooping(false);
          // Increment view count when video starts playing
          widget.reel.views++;
          // widget.controller!.setSubtitleTrack(SubtitleTrack.network('https://example.com/subtitles.srt')); // Uncomment for captions
        });
      }).catchError((error) {
        setState(() {
          _isLoading = true;
        });
      });
      widget.controller!.addListener(() {
        if (widget.controller!.value.isInitialized) {
          setState(() {
            _currentPosition = widget.controller!.value.position;
            _totalDuration = widget.controller!.value.duration;
            _progress = _totalDuration.inMilliseconds == 0
                ? 0.0
                : _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
          });
        }
      });
    }
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _buttonAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _skeletonController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _skeletonController.dispose();
    _confettiController.dispose();
    if (_isFullScreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
    super.dispose();
  }

  void _handleInteraction(String action) {
    _animationController.forward().then((_) => _animationController.reverse());
    // FirebaseAnalytics.instance.logEvent(name: action, parameters: {'reel_id': widget.reel.id}); // Uncomment for analytics
    if (action == 'Like') {
      HapticFeedback.mediumImpact();
      setState(() {
        _likes++;
        _addReaction('❤️');
      });
    } else if (action == 'Comment') {
      widget.onComment();
    } else if (action == 'Share') {
      widget.onShare();
    } else if (action == 'Follow') {
      HapticFeedback.lightImpact();
      widget.onFollow();
    }
  }

  void _addReaction(String emoji) {
    setState(() {
      _reactions.add(
          {'emoji': emoji, 'offset': Offset(0.0, -(_reactions.length * 60.0))});
      Future.delayed(const Duration(seconds: 2), () {
        setState(() {
          _reactions.removeWhere((r) => r['emoji'] == emoji);
        });
      });
    });
  }

  void _submitAnswer(String answer) {
    setState(() {
      _selectedAnswer = answer;
      _isCorrect = answer == widget.reel.correctAnswer;
      widget.onQuizAnswered(_isCorrect!);
      if (_isCorrect == true) {
        _confettiController.play();
      }
    });
  }

  void _toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
      if (_isFullScreen) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      } else {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      }
    });
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
      widget.controller?.setVolume(_isMuted ? 0.0 : 1.0);
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
      if (_isPlaying) {
        widget.controller?.play();
      } else {
        widget.controller?.pause();
      }
    });
  }

  void _changeQuality(String quality) {
    // Notify the parent (ReelsScreenState) about the quality change.
    // The parent will handle controller disposal, creation, and state update.
    if (widget.onQualityChanged != null) {
      HapticFeedback.lightImpact();
      setState(() {
        _isLoading = true;
      });
      widget.onQualityChanged!(quality);
      // Optionally, show a loading indicator immediately in ReelWidget if desired,
      // but the actual controller change will come from the parent.
      // setState(() {
      //   _isLoading = true;
      // });
    } else {
      print("onQualityChanged callback is null in ReelWidget");
    }
  }

  Widget _buildSkeletonLoader() {
    return AnimatedBuilder(
      animation: _skeletonController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.grey[800]!, Colors.grey[700]!, Colors.grey[800]!],
              stops: const [0.1, 0.3, 0.4],
              begin: Alignment(-1 + 2 * _skeletonController.value, -1),
              end: Alignment(1 + 2 * _skeletonController.value, 1),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton(
      BuildContext ctx, {
        required IconData icon,
        required Color bgColor,
        required VoidCallback onPressed,
        String? count,
        String semanticLabel = '',
        bool isActive = false,
      }) {
    return Semantics(
      label: semanticLabel,
      button: true,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [bgColor.withOpacity(0.9), bgColor.withOpacity(0.6)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              boxShadow: [
                BoxShadow(
                  color: bgColor.withOpacity(0.6),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(icon,
                  color: isActive ? Colors.white : Colors.white70, size: 28),
              onPressed: onPressed,
              tooltip: semanticLabel,
            ),
          ),
          if (count != null) ...[
            const SizedBox(height: 4),
            Text(
              count,
              style: Theme.of(ctx).textTheme.bodyMedium?.copyWith(color: Colors.white),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    int hours = duration.inHours;
    int minutes = duration.inMinutes % 60;
    int seconds = duration.inSeconds % 60;
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Video Player
        GestureDetector(
          onDoubleTap: () => _handleInteraction('Like'),
          onLongPress: _toggleFullScreen,
          onTap: _togglePlayPause,
          child: widget.controller == null || _isLoading
              ? _buildSkeletonLoader()
              : Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blueAccent, Colors.black],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Center(
              child: AspectRatio(
                aspectRatio: widget.controller!.value.aspectRatio,
                child: Stack(
                  children: [
                    VideoPlayer(widget.controller!),
                    Center(
                      child: AnimatedOpacity(
                        opacity: _isPlaying ? 0.0 : 1.0,
                        duration: const Duration(milliseconds: 300),
                        child: Icon(
                          _isPlaying ? Icons.play_arrow : Icons.pause,
                          color: Colors.white.withOpacity(0.7),
                          size: 80,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // Buffering Indicator
        if (widget.controller != null && widget.controller!.value.isBuffering)
          Positioned.fill(
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
          ),
        if (widget.controller != null &&
            widget.controller!.value.isBuffering)
          Positioned.fill(
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
          ),
        if (_isFullScreen)
          Positioned(
            top: 10,
            left: 10,
            child: IconButton(
              icon: const Icon(Icons.fullscreen_exit,
                  color: Colors.white, size: 30),
              onPressed: _toggleFullScreen,
              tooltip: 'Exit fullscreen',
            ),
          ),
        Positioned(
          top: 30,
          left: 10,
          child: Text(
            '${widget.reel.views} views',
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
        ),
        Positioned(
          top: 50,
          right: 10,
          child: PopupMenuButton<String>(
            icon: const Icon(Icons.settings, color: Colors.white),
            onSelected: _changeQuality,
            itemBuilder: (context) => widget.reel.videoUrls.keys
                .map((quality) => PopupMenuItem(
              value: quality,
              child: Text(quality),
            ))
                .toList(),
          ),
        ),
        Positioned(
          top: 50,
          right: 10,
          child: PopupMenuButton<double>(
            icon: const Icon(Icons.speed, color: Colors.white),
            onSelected: (speed) {
              widget.controller?.setPlaybackSpeed(speed);
            },
            itemBuilder: (context) => [0.5, 1.0, 1.5, 2.0]
                .map((speed) => PopupMenuItem(
              value: speed,
              child: Text('${speed}x'),
            ))
                .toList(),
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          left: 0,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VideoProgressIndicator(
                widget.controller!,
                allowScrubbing: true,
                colors: VideoProgressColors(
                  playedColor: Colors.red,
                  bufferedColor: Colors.grey,
                  backgroundColor: Colors.transparent,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(_formatDuration(_currentPosition),
                        style: const TextStyle(color: Colors.white, fontSize: 12)),
                    Text(_formatDuration(_totalDuration - _currentPosition),
                        style: const TextStyle(color: Colors.white70, fontSize: 12)),
                  ],
                ),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: 100,
          left: 20,
          child: Semantics(
            label: 'Interaction buttons',
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ScaleTransition(
                  scale: _buttonAnimation,
                  child: _buildActionButton(
                    context,
                    icon: Icons.favorite_rounded,
                    bgColor: Colors.pinkAccent,
                    onPressed: () => _handleInteraction('Like'),
                    count: _likes.toString(),
                    semanticLabel: 'Like video',
                    isActive: _likes > widget.reel.likes,
                  ),
                ),
                const SizedBox(height: 20),
                ScaleTransition(
                  scale: _buttonAnimation,
                  child: _buildActionButton(
                    context,
                    icon: Icons.chat_bubble_rounded,
                    bgColor: Colors.lightBlueAccent,
                    onPressed: () => _handleInteraction('Comment'),
                    count: widget.reel.comments.toString(),
                    semanticLabel: 'Comment',
                  ),
                ),
                const SizedBox(height: 20),
                ScaleTransition(
                  scale: _buttonAnimation,
                  child: _buildActionButton(
                    context,
                    icon: Icons.share_rounded,
                    bgColor: Colors.greenAccent,
                    onPressed: () => _handleInteraction('Share'),
                    count: widget.reel.shares.toString(),
                    semanticLabel: 'Share',
                  ),
                ),
                const SizedBox(height: 20),
                ScaleTransition(
                  scale: _buttonAnimation,
                  child: _buildActionButton(
                    context,
                    icon: _isMuted ? Icons.volume_off_rounded : Icons.volume_up_rounded,
                    bgColor: Colors.orangeAccent,
                    onPressed: _toggleMute,
                    semanticLabel: _isMuted ? 'Unmute' : 'Mute',
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: 20,
          left: 20,
          child: Semantics(
            label: 'Video information',
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(15),
                boxShadow: const [
                  BoxShadow(
                      color: Colors.black54,
                      blurRadius: 10,
                      offset: Offset(0, 5)),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => UserProfilePage(
                                  username: widget.reel.username),
                            ),
                          );
                        },
                        child: CircleAvatar(
                          radius: 20,
                          backgroundColor: Colors.blueAccent,
                          child: Text(widget.reel.username[0]),
                        ),
                      ),
                      const SizedBox(width: 10),
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => UserProfilePage(
                                  username: widget.reel.username),
                            ),
                          );
                        },
                        child: Text(
                          widget.reel.username,
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () => _handleInteraction('Follow'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: widget.reel.isFollowing
                              ? Colors.grey
                              : Colors.blueAccent,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: Text(
                          widget.reel.isFollowing ? 'Following' : 'Follow',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                  Text(
                    widget.reel.description,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    widget.reel.educationalContent,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'Views: ${widget.reel.views}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
        ),
        if (widget.controller != null &&
            widget.controller!.value.isInitialized &&
            widget.controller!.value.position >=
                widget.controller!.value.duration &&
            !_showQuiz)
          Positioned.fill(
            child: Center(
              child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    _showQuiz = true;
                    widget.controller!.pause();
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blueAccent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: const Text('Take Quiz'),
              ),
            ),
          ),
        if (_showQuiz)
          Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(15),
                boxShadow: const [
                  BoxShadow(
                      color: Colors.black54,
                      blurRadius: 10,
                      offset: Offset(0, 5)),
                ],
              ),
              child: Stack(
                alignment: Alignment.topCenter,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        widget.reel.quizQuestion,
                        style: Theme.of(context).textTheme.titleLarge,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      ...widget.reel.quizOptions.map((option) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _selectedAnswer == option
                                ? (_isCorrect == true
                                ? Colors.green
                                : Colors.red)
                                : Colors.blueAccent,
                            minimumSize: const Size(double.infinity, 50),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            elevation: 5,
                          ),
                          onPressed: _selectedAnswer == null
                              ? () => _submitAnswer(option)
                              : null,
                          child: Text(option),
                        ),
                      )),
                      if (_selectedAnswer != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 20),
                          child: Text(
                            _isCorrect == true
                                ? 'Correct Answer!'
                                : 'Wrong Answer, Try Again!',
                            style: TextStyle(
                              color: _isCorrect == true
                                  ? Colors.green
                                  : Colors.red,
                              fontSize: 18,
                              shadows: const [
                                Shadow(
                                    blurRadius: 2.0,
                                    color: Colors.black54,
                                    offset: Offset(1, 1)),
                              ],
                            ),
                          ),
                        ),
                      if (_selectedAnswer != null)
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blueAccent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            elevation: 5,
                          ),
                          onPressed: () {
                            setState(() {
                              _showQuiz = false;
                              _selectedAnswer = null;
                              _isCorrect = null;
                              widget.controller?.seekTo(Duration.zero);
                              widget.controller?.play();
                            });
                          },
                          child: const Text('Continue'),
                        ),
                    ],
                  ),
                  ConfettiWidget(
                    confettiController: _confettiController,
                    blastDirection: pi / 2,
                    emissionFrequency: 0.05,
                    numberOfParticles: 20,
                    maxBlastForce: 100,
                    minBlastForce: 80,
                  ),
                ],
              ),
            ),
          ),
        ..._reactions.map((reaction) {
          return AnimatedPositioned(
            duration: const Duration(seconds: 2),
            bottom: 100,
            right: 20,
            child: AnimatedOpacity(
              opacity: 1.0,
              duration: const Duration(seconds: 2),
              child: Text(
                reaction['emoji'],
                style: const TextStyle(fontSize: 30),
              ),
            ),
          );
        }),
      ],
    );
  }
}

class CommentDatabase {
  static final CommentDatabase instance = CommentDatabase._init();
  static Database? _database;

  CommentDatabase._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('comments.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);
    return await openDatabase(path, version: 1, onCreate: _createDB);
  }

  Future _createDB(Database db, int version) async {
    await db.execute('''
    CREATE TABLE comments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      reelId INTEGER,
      content TEXT,
      timestamp TEXT
    )
    ''');
  }

  Future<void> addComment(int reelId, String content) async {
    final db = await database;
    await db.insert('comments', {
      'reelId': reelId,
      'content': content,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  Future<List<Map<String, dynamic>>> getComments(int reelId) async {
    final db = await database;
    return await db.query('comments', where: 'reelId = ?', whereArgs: [reelId]);
  }
}

class CommentSection extends StatefulWidget {
  final Reel reel;

  const CommentSection({super.key, required this.reel});

  @override
  _CommentSectionState createState() => _CommentSectionState();
}

class _CommentSectionState extends State<CommentSection> {
  final TextEditingController _commentController = TextEditingController();
  final List<String> _comments = [];
  bool _isLoadingComments = true;

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  void _loadComments() async {
    setState(() {
      _isLoadingComments = true;
    });
    final comments = await CommentDatabase.instance.getComments(widget.reel.id);
    setState(() {
      _comments.addAll(comments.map((c) => c['content'] as String));
      _isLoadingComments = false;
    });
  }

  void _addComment() {
    if (_commentController.text.isNotEmpty) {
      CommentDatabase.instance
          .addComment(widget.reel.id, _commentController.text);
      setState(() {
        _comments.add(_commentController.text);
        widget.reel.comments++;
        _commentController.clear();
      });
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.5,
      maxChildSize: 0.9,
      builder: (context, scrollController) => Container(
        decoration: const BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 5,
              margin: const EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                color: Colors.grey[600],
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            Expanded(
              child: _isLoadingComments
                  ? const Center(
                  child: CircularProgressIndicator(color: Colors.white))
                  : ListView.builder(
                controller: scrollController,
                itemCount: _comments.length,
                itemBuilder: (context, index) => ListTile(
                  title: Text(
                    _comments[index],
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  leading: const CircleAvatar(
                    backgroundColor: Colors.blueAccent,
                    child: Text('U'),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(10),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _commentController,
                      decoration: InputDecoration(
                        hintText: 'Add a comment...',
                        hintStyle: Theme.of(context).textTheme.bodyMedium,
                        filled: true,
                        fillColor: Colors.white.withOpacity(0.1),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.send, color: Colors.blueAccent),
                    onPressed: _addComment,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class UserProfilePage extends StatelessWidget {
  final String username;
  const UserProfilePage({super.key, required this.username});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('$username\'s Profile')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 50,
              child: Text(username[0], style: const TextStyle(fontSize: 40)),
            ),
            const SizedBox(height: 20),
            Text(
              username,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 20),
            Text(
              'This user has posted reels', // Update with actual reel count if needed
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
