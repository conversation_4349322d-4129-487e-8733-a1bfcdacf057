import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:untitled10/models/post_model.dart';

class PostService {
  static const String _baseUrl = 'YOUR_API_BASE_URL';
  final String _token;

  PostService(this._token);

  Future<List<Post>> fetchPosts() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/posts'),
        headers: {'Authorization': 'Bearer $_token'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Post.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load posts');
      }
    } catch (e) {
      throw Exception('Error fetching posts: $e');
    }
  }

  Future<Post> createPost(Post post) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/posts'),
        headers: {
          'Authorization': 'Bearer $_token',
          'Content-Type': 'application/json',
        },
        body: json.encode(post.toJson()),
      );

      if (response.statusCode == 201) {
        return Post.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to create post');
      }
    } catch (e) {
      throw Exception('Error creating post: $e');
    }
  }

  Future<void> likePost(String postId) async {
    try {
      await http.post(
        Uri.parse('$_baseUrl/posts/$postId/like'),
        headers: {'Authorization': 'Bearer $_token'},
      );
    } catch (e) {
      throw Exception('Error liking post: $e');
    }
  }

  // يمكنك إضافة المزيد من الوظائف مثل حذف المنشور، التحديث، إلخ.
}
