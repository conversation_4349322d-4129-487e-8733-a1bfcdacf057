// ملف إعداد التكامل للتطبيق
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/reel_provider.dart';
import 'appstate.dart';

// كلاس مساعد لإعداد التكامل
class AppIntegrationSetup {
  
  // إعداد Providers المطلوبة
  static List<ChangeNotifierProvider> getProviders() {
    return [
      ChangeNotifierProvider(create: (context) => AppState()),
      ChangeNotifierProvider(create: (context) => ReelProvider()),
      // يمكن إضافة المزيد من الـ providers هنا
    ];
  }

  // إعداد التطبيق مع جميع الـ Providers
  static Widget setupApp({required Widget child}) {
    return MultiProvider(
      providers: getProviders(),
      child: child,
    );
  }

  // فحص التكامل والتأكد من جاهزية النظام
  static Future<bool> checkIntegration() async {
    try {
      // فحص الاتصال بالباك إند
      final backendUrl = AppState.getBackendUrl();
      if (backendUrl.isEmpty) {
        print('❌ خطأ: لم يتم تعيين URL الباك إند');
        return false;
      }

      // فحص التوكن (اختياري)
      final token = AppState.getCurrentToken();
      if (token == null) {
        print('⚠️ تحذير: لا يوجد توكن مصادقة');
      }

      print('✅ التكامل جاهز للعمل');
      return true;
    } catch (e) {
      print('❌ خطأ في فحص التكامل: $e');
      return false;
    }
  }

  // إعداد الثيمات
  static ThemeData getLightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
      ),
    );
  }

  static ThemeData getDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      ),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
      ),
    );
  }

  // رسائل التحقق من التكامل
  static void showIntegrationStatus(BuildContext context) {
    checkIntegration().then((isReady) {
      final message = isReady 
          ? 'التكامل جاهز للعمل ✅'
          : 'يوجد مشاكل في التكامل ❌';
      
      final color = isReady ? Colors.green : Colors.red;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: color,
          duration: const Duration(seconds: 3),
        ),
      );
    });
  }

  // دليل الاستخدام السريع
  static void showQuickGuide(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('دليل الاستخدام السريع'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('🎥 الفيديوهات التعليمية:', 
                   style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• اسحب عمودياً للتنقل بين الفيديوهات'),
              Text('• اضغط على الشاشة لإيقاف/تشغيل الفيديو'),
              Text('• اضغط مرتين للإعجاب'),
              SizedBox(height: 16),
              
              Text('💬 التفاعلات:', 
                   style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• اضغط على القلب للإعجاب'),
              Text('• اضغط على التعليق لإضافة تعليق'),
              Text('• اضغط على الحفظ لحفظ الفيديو'),
              Text('• اضغط على + لمتابعة المستخدم'),
              SizedBox(height: 16),
              
              Text('🧠 الكويز التعليمي:', 
                   style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• يظهر الكويز عند انتهاء الفيديو'),
              Text('• اختر الإجابة الصحيحة لكسب النقاط'),
              Text('• راجع الشرح بعد الإجابة'),
              SizedBox(height: 16),
              
              Text('🔍 البحث والفلاتر:', 
                   style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• اضغط على أيقونة البحث للبحث'),
              Text('• استخدم الفلاتر لتصنيف المحتوى'),
              Text('• اختر مستوى الصعوبة المناسب'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  // معلومات التطبيق والتكامل
  static void showAppInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات التطبيق'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('📱 منصة فلك للفيديوهات التعليمية', 
                   style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
              SizedBox(height: 16),
              
              Text('🔧 التقنيات المستخدمة:', 
                   style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• Flutter للواجهة الأمامية'),
              Text('• Node.js + Express للباك إند'),
              Text('• MongoDB لقاعدة البيانات'),
              Text('• Socket.IO للتحديثات المباشرة'),
              Text('• Provider لإدارة الحالة'),
              SizedBox(height: 16),
              
              Text('✨ الميزات الرئيسية:', 
                   style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• فيديوهات تعليمية تفاعلية'),
              Text('• كويز ونقاط تعليمية'),
              Text('• تفاعلات اجتماعية'),
              Text('• بحث وفلاتر متقدمة'),
              Text('• جودات فيديو متعددة'),
              Text('• تحديثات فورية'),
              SizedBox(height: 16),
              
              Text('📊 الإحصائيات:', 
                   style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• 17+ فئة تعليمية'),
              Text('• 4 مستويات صعوبة'),
              Text('• دعم جودات 360p-1080p'),
              Text('• تكامل كامل مع الباك إند'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // إعدادات التطبيق
  static Widget buildSettingsPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: ListView(
        children: [
          ListTile(
            leading: const Icon(Icons.info_outline),
            title: const Text('معلومات التطبيق'),
            subtitle: const Text('تفاصيل التطبيق والتقنيات المستخدمة'),
            onTap: () => showAppInfo(context),
          ),
          ListTile(
            leading: const Icon(Icons.help_outline),
            title: const Text('دليل الاستخدام'),
            subtitle: const Text('كيفية استخدام التطبيق'),
            onTap: () => showQuickGuide(context),
          ),
          ListTile(
            leading: const Icon(Icons.check_circle_outline),
            title: const Text('حالة التكامل'),
            subtitle: const Text('فحص التكامل مع الباك إند'),
            onTap: () => showIntegrationStatus(context),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.video_settings),
            title: const Text('إعدادات الفيديو'),
            subtitle: const Text('جودة الفيديو والتشغيل التلقائي'),
            onTap: () {
              // يمكن إضافة صفحة إعدادات الفيديو
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('قريباً...')),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.notifications_outlined),
            title: const Text('إعدادات الإشعارات'),
            subtitle: const Text('إدارة الإشعارات والتنبيهات'),
            onTap: () {
              // يمكن إضافة صفحة إعدادات الإشعارات
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('قريباً...')),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.privacy_tip_outlined),
            title: const Text('الخصوصية والأمان'),
            subtitle: const Text('إعدادات الخصوصية والحماية'),
            onTap: () {
              // يمكن إضافة صفحة إعدادات الخصوصية
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('قريباً...')),
              );
            },
          ),
        ],
      ),
    );
  }

  // صفحة اختبار التكامل
  static Widget buildTestPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار التكامل'),
      ),
      body: Consumer<ReelProvider>(
        builder: (context, reelProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'حالة الاتصال',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              reelProvider.isConnected 
                                  ? Icons.wifi 
                                  : Icons.wifi_off,
                              color: reelProvider.isConnected 
                                  ? Colors.green 
                                  : Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              reelProvider.isConnected 
                                  ? 'متصل' 
                                  : 'غير متصل',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'إحصائيات البيانات',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Text('عدد الفيديوهات: ${reelProvider.reels.length}'),
                        Text('الفئة الحالية: ${reelProvider.currentCategory}'),
                        Text('مستوى الصعوبة: ${reelProvider.currentDifficulty}'),
                        Text('حالة التحميل: ${reelProvider.isLoading ? "جاري التحميل" : "مكتمل"}'),
                        if (reelProvider.hasError)
                          Text(
                            'خطأ: ${reelProvider.errorMessage}',
                            style: const TextStyle(color: Colors.red),
                          ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => reelProvider.loadReels(refresh: true),
                    child: const Text('تحديث البيانات'),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
